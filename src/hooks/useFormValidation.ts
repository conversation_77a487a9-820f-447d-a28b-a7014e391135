import { useState, useCallback } from "react";
import {
  validateForm,
  validateField,
  ValidationSchema,
  ValidationErrors,
  hasValidationErrors,
} from "../utils/validation";

interface UseFormValidationOptions {
  schema: ValidationSchema;
}

interface UseFormValidationReturn {
  errors: ValidationErrors;
  isValid: boolean;
  validateField: (field: string, value: any) => void;
  validateForm: (data: Record<string, any>) => boolean;
  clearErrors: () => void;
  clearFieldError: (field: string) => void;
  setFieldError: (field: string, error: string) => void;
}

export function useFormValidation({
  schema,
  validateOnChange = false,
  validateOnBlur = true,
}: UseFormValidationOptions): UseFormValidationReturn {
  const [errors, setErrors] = useState<ValidationErrors>({});

  const validateFieldCallback = useCallback(
    (field: string, value: any) => {
      const rule = schema[field];
      if (!rule) return;

      const error = validateField(value, rule);
      setErrors((prev) => ({
        ...prev,
        [field]: error || "",
      }));
    },
    [schema]
  );

  const validateFormCallback = useCallback(
    (data: Record<string, any>) => {
      const newErrors = validateForm(data, schema);
      setErrors(newErrors);
      return !hasValidationErrors(newErrors);
    },
    [schema]
  );

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const setFieldError = useCallback((field: string, error: string) => {
    setErrors((prev) => ({
      ...prev,
      [field]: error,
    }));
  }, []);

  const isValid = !hasValidationErrors(errors);

  return {
    errors,
    isValid,
    validateField: validateFieldCallback,
    validateForm: validateFormCallback,
    clearErrors,
    clearFieldError,
    setFieldError,
  };
}
