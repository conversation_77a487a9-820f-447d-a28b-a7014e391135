import React, { useEffect, useState } from "react";
import { Bell, Lock, User } from "lucide-react";
import { highHeel } from "@lucide/lab";
import { useStore } from "../lib/store";
import { supabase } from "../lib/supabase";

export function AdminProfile() {
  const {
    adminProfile,
    adminPreferences,
    loading,
    error,
    fetchAdminProfile,
    fetchAdminPreferences,
    updateAdminProfile,
    updateAdminPreferences,
  } = useStore();

  const [formData, setFormData] = useState({
    name: "",
    title: "",
    email: "",
    phone: "",
  });

  const [notificationPrefs, setNotificationPrefs] = useState({
    email_notifications: true,
    new_applications: true,
    status_updates: true,
    daily_digest: false,
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    fetchAdminProfile();
    fetchAdminPreferences();
  }, []);

  useEffect(() => {
    if (adminProfile) {
      setFormData({
        name: adminProfile.name || "",
        title: adminProfile.title || "",
        email: adminProfile.email || "",
        phone: adminProfile.phone || "",
      });
    }
  }, [adminProfile]);

  useEffect(() => {
    if (adminPreferences) {
      setNotificationPrefs({
        email_notifications: adminPreferences.email_notifications,
        new_applications: adminPreferences.new_applications,
        status_updates: adminPreferences.status_updates,
        daily_digest: adminPreferences.daily_digest,
      });
    }
  }, [adminPreferences]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateAdminProfile(formData);
      setSuccessMessage("Profile updated successfully");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err) {
      console.error("Error updating profile:", err);
      setErrorMessage("Failed to update profile");
      setTimeout(() => setErrorMessage(""), 3000);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage("");

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setErrorMessage("New passwords do not match");
      return;
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword,
      });

      if (error) throw error;

      setSuccessMessage("Password updated successfully");
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (err: any) {
      console.error("Error updating password:", err);
      setErrorMessage(err.message || "Failed to update password");
    }
  };

  const handleNotificationChange = async (
    key: keyof typeof notificationPrefs
  ) => {
    const updatedPrefs = {
      ...notificationPrefs,
      [key]: !notificationPrefs[key as keyof typeof notificationPrefs],
    };
    setNotificationPrefs(updatedPrefs);

    try {
      await updateAdminPreferences(updatedPrefs);
    } catch (err) {
      console.error("Error updating preferences:", err);
      setErrorMessage("Failed to update notification preferences");
      setTimeout(() => setErrorMessage(""), 3000);
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-600 text-center py-8">{error}</div>;
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Profile Information */}
      <div className="card">
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <User className="w-8 h-8 text-champagne" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold text-dark">
              {formData.name || "Admin User"}
            </h2>
            <p className="text-gray-600">{formData.title || "Spa Manager"}</p>
          </div>
        </div>

        {successMessage && (
          <div className="mb-6 p-3 bg-green-50 text-green-700 rounded-md">
            {successMessage}
          </div>
        )}

        {errorMessage && (
          <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md">
            {errorMessage}
          </div>
        )}

        <form
          onSubmit={handleSubmit}
          className="space-y-6">
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700 mb-1">
              Full Name
            </label>
            <input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="input"
              required
            />
          </div>

          <div>
            <label
              htmlFor="title"
              className="block text-sm font-medium text-gray-700 mb-1">
              Job Title
            </label>
            <input
              id="title"
              type="text"
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              className="input"
              required
            />
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              className="input"
              required
            />
          </div>

          <div>
            <label
              htmlFor="phone"
              className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number
            </label>
            <input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              className="input"
              required
            />
          </div>

          <div>
            <button
              type="submit"
              className="btn-primary">
              Save Changes
            </button>
          </div>
        </form>
      </div>

      {/* Notification Preferences */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <Bell className="w-6 h-6 text-champagne" />
          <h3 className="text-xl font-semibold text-dark">
            Notification Preferences
          </h3>
        </div>

        <div className="space-y-4">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={notificationPrefs.email_notifications}
              onChange={() => handleNotificationChange("email_notifications")}
              className="rounded border-gray-300 text-champagne focus:ring-champagne"
            />
            <span className="text-gray-700">Enable Email Notifications</span>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={notificationPrefs.new_applications}
              onChange={() => handleNotificationChange("new_applications")}
              className="rounded border-gray-300 text-champagne focus:ring-champagne"
            />
            <span className="text-gray-700">New Application Alerts</span>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={notificationPrefs.status_updates}
              onChange={() => handleNotificationChange("status_updates")}
              className="rounded border-gray-300 text-champagne focus:ring-champagne"
            />
            <span className="text-gray-700">Status Update Notifications</span>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={notificationPrefs.daily_digest}
              onChange={() => handleNotificationChange("daily_digest")}
              className="rounded border-gray-300 text-champagne focus:ring-champagne"
            />
            <span className="text-gray-700">Daily Digest Email</span>
          </label>
        </div>
      </div>

      {/* Password Management */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <Lock className="w-6 h-6 text-champagne" />
          <h3 className="text-xl font-semibold text-dark">Change Password</h3>
        </div>

        <form
          onSubmit={handlePasswordChange}
          className="space-y-4">
          <div>
            <label
              htmlFor="currentPassword"
              className="block text-sm font-medium text-gray-700 mb-1">
              Current Password
            </label>
            <input
              id="currentPassword"
              type="password"
              value={passwordData.currentPassword}
              onChange={(e) =>
                setPasswordData({
                  ...passwordData,
                  currentPassword: e.target.value,
                })
              }
              className="input"
              required
            />
          </div>

          <div>
            <label
              htmlFor="newPassword"
              className="block text-sm font-medium text-gray-700 mb-1">
              New Password
            </label>
            <input
              id="newPassword"
              type="password"
              value={passwordData.newPassword}
              onChange={(e) =>
                setPasswordData({
                  ...passwordData,
                  newPassword: e.target.value,
                })
              }
              className="input"
              required
            />
          </div>

          <div>
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium text-gray-700 mb-1">
              Confirm New Password
            </label>
            <input
              id="confirmPassword"
              type="password"
              value={passwordData.confirmPassword}
              onChange={(e) =>
                setPasswordData({
                  ...passwordData,
                  confirmPassword: e.target.value,
                })
              }
              className="input"
              required
            />
          </div>

          <button
            type="submit"
            className="btn-primary">
            Update Password
          </button>
        </form>
      </div>
    </div>
  );
}
