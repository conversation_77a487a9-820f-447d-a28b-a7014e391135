import { useEffect } from "react";
import { useAdminStore } from "../stores";
import { ProfileForm, NotificationSettings, PasswordForm } from "./admin";
import { AsyncErrorBoundary } from "./ui";

export function AdminProfile() {
  const {
    adminProfile,
    adminPreferences,
    loading,
    error,
    fetchAdminProfile,
    fetchAdminPreferences,
    updateAdminProfile,
    updateAdminPreferences,
  } = useAdminStore();

  useEffect(() => {
    fetchAdminProfile();
    fetchAdminPreferences();
  }, [fetchAdminProfile, fetchAdminPreferences]);

  const handleRetry = () => {
    fetchAdminProfile();
    fetchAdminPreferences();
  };

  return (
    <AsyncErrorBoundary
      error={error}
      loading={loading}
      onRetry={handleRetry}>
      <div className="max-w-2xl mx-auto space-y-6">
        <ProfileForm
          adminProfile={adminProfile}
          onSubmit={updateAdminProfile}
          loading={loading}
        />

        <NotificationSettings
          adminPreferences={adminPreferences}
          onUpdate={updateAdminPreferences}
          loading={loading}
        />

        <PasswordForm />
      </div>
    </AsyncErrorBoundary>
  );
}
