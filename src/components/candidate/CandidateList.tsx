import React, { useEffect, useMemo, useCallback } from "react";
import { Link } from "react-router-dom";
import { Flag, Star } from "lucide-react";
import { useCandidateStore, useCommonStore } from "../stores";
import { AsyncErrorBoundary } from "../ui";

export function CandidateList({ archived = false }) {
  const {
    candidates,
    filters,
    loading: candidateLoading,
    error: candidateError,
    fetchCandidates,
    setFilters,
  } = useCandidateStore();

  const {
    positions,
    locations,
    loading: commonLoading,
    error: commonError,
    fetchPositions,
    fetchLocations,
  } = useCommonStore();

  const loading = candidateLoading || commonLoading;
  const error = candidateError || commonError;

  useEffect(() => {
    fetchCandidates();
    fetchPositions();
    fetchLocations();
  }, []);

  // Memoize filtered candidates for performance
  const filteredCandidates = useMemo(() => {
    return candidates.filter((candidate) => {
      if (archived && candidate.status !== "archived") return false;
      if (!archived && candidate.status === "archived") return false;

      if (filters.position && candidate.position_id !== filters.position)
        return false;
      if (filters.location && candidate.location_id !== filters.location)
        return false;
      if (filters.status && candidate.status !== filters.status) return false;

      return true;
    });
  }, [candidates, archived, filters]);

  const handleRetry = useCallback(() => {
    fetchCandidates();
    fetchPositions();
    fetchLocations();
  }, [fetchCandidates, fetchPositions, fetchLocations]);

  return (
    <AsyncErrorBoundary
      error={error}
      loading={loading}
      onRetry={handleRetry}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-dark">
            {archived ? "Archived Candidates" : "All Candidates"}
          </h2>

          {/* Filters */}
          <div className="flex items-center space-x-4">
            <select
              className="input max-w-xs"
              value={filters.position}
              onChange={(e) => setFilters({ position: e.target.value })}>
              <option value="">All Positions</option>
              {positions.map((position) => (
                <option
                  key={position.id}
                  value={position.id}>
                  {position.title}
                </option>
              ))}
            </select>

            <select
              className="input max-w-xs"
              value={filters.location}
              onChange={(e) => setFilters({ location: e.target.value })}>
              <option value="">All Locations</option>
              {locations.map((location) => (
                <option
                  key={location.id}
                  value={location.id}>
                  {location.name}
                </option>
              ))}
            </select>

            <select
              className="input max-w-xs"
              value={filters.status}
              onChange={(e) => setFilters({ status: e.target.value })}>
              <option value="">All Statuses</option>
              <option value="new">New</option>
              <option value="in_progress">In Progress</option>
              <option value="on_hold">On Hold</option>
              <option value="hired">Hired</option>
              <option value="declined">Declined</option>
              {!archived && <option value="archived">Archived</option>}
            </select>
          </div>
        </div>

        {/* Candidate Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCandidates.map((candidate) => (
            <div
              key={candidate.id}
              className="card hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-dark">
                    {candidate.full_name}
                    {candidate.is_new && (
                      <span className="ml-2 px-2 py-1 text-xs bg-champagne/10 text-champagne rounded-full">
                        New
                      </span>
                    )}
                  </h3>
                  <p className="text-gray-600">{candidate.position?.title}</p>
                </div>
                <button className="text-gray-400 hover:text-champagne">
                  <Flag className="w-5 h-5" />
                </button>
              </div>

              <div className="flex items-center space-x-1 mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className="w-4 h-4 text-champagne"
                    fill={star <= candidate.rating ? "currentColor" : "none"}
                  />
                ))}
              </div>

              <div className="flex items-center justify-between">
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${
                    candidate.status === "new"
                      ? "bg-blue-100 text-blue-800"
                      : candidate.status === "in_progress"
                      ? "bg-yellow-100 text-yellow-800"
                      : candidate.status === "hired"
                      ? "bg-green-100 text-green-800"
                      : candidate.status === "declined"
                      ? "bg-red-100 text-red-800"
                      : candidate.status === "on_hold"
                      ? "bg-orange-100 text-orange-800"
                      : "bg-gray-100 text-gray-800"
                  }`}>
                  {candidate.status.replace("_", " ").charAt(0).toUpperCase() +
                    candidate.status.replace("_", " ").slice(1)}
                </span>
                <Link
                  to={`/dashboard/candidates/${candidate.id}`}
                  className="text-champagne hover:text-champagne/80">
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </AsyncErrorBoundary>
  );
}
