import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import { Star, Phone, Mail, MapPin, Calendar } from "lucide-react";
import { useCandidateStore } from "../stores";
import { format } from "date-fns";

export function CandidateDetails() {
  const { id } = useParams();
  const {
    candidates,
    applicationLogs,
    loading,
    error,
    fetchCandidates,
    fetchApplicationLogs,
    updateCandidateStatus,
    updateCandidateRating,
    addApplicationLog,
  } = useCandidateStore();

  useEffect(() => {
    fetchCandidates();
    if (id) {
      fetchApplicationLogs(id);
    }
  }, [id]);

  const candidate = candidates.find((c) => c.id === id);

  if (loading) {
    return <div className="text-center py-8">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-600 text-center py-8">{error}</div>;
  }

  if (!candidate) {
    return <div className="text-center py-8">Candidate not found</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-dark">
            {candidate.full_name}
          </h2>
          <p className="text-gray-600">{candidate.position?.title}</p>
        </div>

        <select
          className="input max-w-xs"
          value={candidate.status}
          onChange={(e) =>
            updateCandidateStatus(candidate.id, e.target.value as any)
          }>
          <option value="new">New</option>
          <option value="in_progress">In Progress</option>
          <option value="on_hold">On Hold</option>
          <option value="hired">Hired</option>
          <option value="declined">Declined</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-dark mb-4">
              Contact Information
            </h3>
            <div className="space-y-3">
              <div className="flex items-center text-gray-600">
                <Mail className="w-5 h-5 mr-2" />
                {candidate.email}
              </div>
              <div className="flex items-center text-gray-600">
                <Phone className="w-5 h-5 mr-2" />
                {candidate.phone}
              </div>
              <div className="flex items-center text-gray-600">
                <MapPin className="w-5 h-5 mr-2" />
                {candidate.location?.name}
              </div>
              <div className="flex items-center text-gray-600">
                <Calendar className="w-5 h-5 mr-2" />
                {candidate.availability?.join(", ") || "Not specified"}
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-dark mb-4">
              Application Timeline
            </h3>
            <div className="space-y-4">
              {applicationLogs.map((log) => (
                <div
                  key={log.id}
                  className="flex items-start">
                  <div className="w-2 h-2 mt-2 rounded-full bg-champagne"></div>
                  <div className="ml-4">
                    <p className="font-medium text-dark">{log.notes}</p>
                    <p className="text-sm text-gray-600">
                      {format(new Date(log.created_at), "MMMM d, yyyy")}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-dark mb-4">Rating</h3>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => updateCandidateRating(candidate.id, star)}>
                  <Star
                    className="w-6 h-6 text-champagne"
                    fill={star <= candidate.rating ? "currentColor" : "none"}
                  />
                </button>
              ))}
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-dark mb-4">Add Note</h3>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const form = e.target as HTMLFormElement;
                const notes = (
                  form.elements.namedItem("notes") as HTMLTextAreaElement
                ).value;
                if (notes.trim()) {
                  addApplicationLog(candidate.id, candidate.status, notes);
                  form.reset();
                }
              }}>
              <textarea
                name="notes"
                className="input min-h-[150px]"
                placeholder="Add a note..."></textarea>
              <button
                type="submit"
                className="btn-primary mt-4">
                Save Note
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
