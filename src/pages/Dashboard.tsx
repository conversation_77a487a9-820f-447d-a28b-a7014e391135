import React from "react";
import { Routes, Route, Link, useNavigate } from "react-router-dom";
import { Users, Star, Archive, LogOut, User, Settings } from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { useStore } from "../lib/store";
import { CandidateList } from "../components/CandidateList";
import { CandidateDetails } from "../components/CandidateDetails";
import { AdminProfile } from "../components/AdminProfile";

export function Dashboard() {
  const { signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200">
        <div className="p-6">
          <h1 className="text-xl font-bold text-dark">Skin Spa New York</h1>
        </div>

        <nav className="mt-6">
          <Link
            to="/dashboard"
            className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50">
            <Users className="w-5 h-5 mr-3 text-champagne" />
            Dashboard
          </Link>

          <Link
            to="/dashboard/candidates"
            className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50">
            <Star className="w-5 h-5 mr-3 text-champagne" />
            Candidates
          </Link>

          <Link
            to="/dashboard/archived"
            className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50">
            <Archive className="w-5 h-5 mr-3 text-champagne" />
            Archived
          </Link>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1">
        <header className="bg-white border-b border-gray-200">
          <div className="px-6 py-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-dark">Dashboard</h2>

            <div className="flex items-center space-x-4">
              <Link
                to="/dashboard/profile"
                className="p-2 text-gray-600 hover:text-dark">
                <User className="w-5 h-5" />
              </Link>

              <Link
                to="/dashboard/settings"
                className="p-2 text-gray-600 hover:text-dark">
                <Settings className="w-5 h-5" />
              </Link>

              <button
                onClick={handleSignOut}
                className="p-2 text-gray-600 hover:text-dark">
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        <div className="p-6">
          <Routes>
            <Route
              index
              element={<DashboardHome />}
            />
            <Route
              path="candidates"
              element={<CandidateList />}
            />
            <Route
              path="candidates/:id"
              element={<CandidateDetails />}
            />
            <Route
              path="archived"
              element={<CandidateList archived />}
            />
            <Route
              path="profile"
              element={<AdminProfile />}
            />
          </Routes>
        </div>
      </main>
    </div>
  );
}

function DashboardHome() {
  const { candidates, fetchCandidates } = useStore();

  React.useEffect(() => {
    fetchCandidates();
  }, [fetchCandidates]);

  // Calculate metrics from real data
  const activeApplications = candidates.filter((c) =>
    ["new", "in_progress", "on_hold"].includes(c.status)
  ).length;

  const totalHires = candidates.filter((c) => c.status === "hired").length;
  const archivedCount = candidates.filter(
    (c) => c.status === "archived"
  ).length;

  // Get recent candidates (last 5)
  const recentCandidates = candidates
    .filter((c) => c.status !== "archived")
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center">
            <Users className="w-10 h-10 text-champagne" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-dark">
                Active Applications
              </h3>
              <p className="text-3xl font-bold text-champagne">
                {activeApplications}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <Star className="w-10 h-10 text-champagne" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-dark">Total Hires</h3>
              <p className="text-3xl font-bold text-champagne">{totalHires}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <Archive className="w-10 h-10 text-champagne" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-dark">Archived</h3>
              <p className="text-3xl font-bold text-champagne">
                {archivedCount}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Candidates */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-dark">Recent Candidates</h3>
          <Link
            to="/dashboard/candidates"
            className="text-champagne hover:text-champagne/80">
            View All
          </Link>
        </div>

        <div className="space-y-4">
          {recentCandidates.length > 0 ? (
            recentCandidates.map((candidate) => (
              <div
                key={candidate.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-dark">
                    {candidate.full_name}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {candidate.position?.title}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      candidate.status === "new"
                        ? "bg-blue-100 text-blue-800"
                        : candidate.status === "in_progress"
                        ? "bg-yellow-100 text-yellow-800"
                        : candidate.status === "hired"
                        ? "bg-green-100 text-green-800"
                        : candidate.status === "declined"
                        ? "bg-red-100 text-red-800"
                        : candidate.status === "on_hold"
                        ? "bg-orange-100 text-orange-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                    {candidate.status
                      .replace("_", " ")
                      .charAt(0)
                      .toUpperCase() +
                      candidate.status.replace("_", " ").slice(1)}
                  </span>
                  <Link
                    to={`/dashboard/candidates/${candidate.id}`}
                    className="text-champagne hover:text-champagne/80 text-sm">
                    View
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500">No recent candidates found.</p>
          )}
        </div>
      </div>
    </div>
  );
}
