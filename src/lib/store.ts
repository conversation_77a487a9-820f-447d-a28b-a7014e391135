import { create } from "zustand";
import { supabase } from "./supabase";
import { format } from "date-fns";
import type {
  Candidate,
  CandidateStatus,
  ApplicationLog,
  Position,
  Location,
  AdminProfile,
  AdminPreferences,
  StoreFilters,
} from "../types";

interface StoreState {
  candidates: Candidate[];
  positions: Position[];
  locations: Location[];
  applicationLogs: ApplicationLog[];
  adminProfile: AdminProfile | null;
  adminPreferences: AdminPreferences | null;
  loading: boolean;
  error: string | null;
  filters: StoreFilters;
  fetchCandidates: () => Promise<void>;
  fetchPositions: () => Promise<void>;
  fetchLocations: () => Promise<void>;
  fetchApplicationLogs: (candidateId: string) => Promise<void>;
  fetchAdminProfile: () => Promise<void>;
  fetchAdminPreferences: () => Promise<void>;
  updateAdminProfile: (profile: Partial<AdminProfile>) => Promise<void>;
  updateAdminPreferences: (
    preferences: Partial<AdminPreferences>
  ) => Promise<void>;
  updateCandidateStatus: (
    candidateId: string,
    status: CandidateStatus
  ) => Promise<void>;
  updateCandidateRating: (candidateId: string, rating: number) => Promise<void>;
  addApplicationLog: (
    candidateId: string,
    status: CandidateStatus,
    notes: string
  ) => Promise<void>;
  setFilters: (filters: Partial<StoreFilters>) => void;
}

export const useStore = create<StoreState>((set, get) => ({
  candidates: [],
  positions: [],
  locations: [],
  applicationLogs: [],
  adminProfile: null,
  adminPreferences: null,
  loading: false,
  error: null,
  filters: {
    position: "",
    location: "",
    status: "",
  },

  fetchCandidates: async () => {
    set({ loading: true, error: null });
    try {
      const { data, error } = await supabase
        .from("candidates")
        .select(
          `
          *,
          position:positions(title),
          location:locations(name)
        `
        )
        .order("created_at", { ascending: false });

      if (error) throw error;
      set({ candidates: data || [] });
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ loading: false });
    }
  },

  fetchPositions: async () => {
    try {
      const { data, error } = await supabase
        .from("positions")
        .select("*")
        .eq("is_active", true)
        .order("title");

      if (error) throw error;
      set({ positions: data || [] });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  fetchLocations: async () => {
    try {
      const { data, error } = await supabase
        .from("locations")
        .select("*")
        .order("name");

      if (error) throw error;
      set({ locations: data || [] });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  fetchApplicationLogs: async (candidateId: string) => {
    try {
      const { data, error } = await supabase
        .from("application_logs")
        .select("*")
        .eq("candidate_id", candidateId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      set({ applicationLogs: data || [] });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  fetchAdminProfile: async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("No user found");

      const { data, error } = await supabase
        .from("admin_profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error) throw error;
      set({ adminProfile: data });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  fetchAdminPreferences: async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("No user found");

      const { data, error } = await supabase
        .from("admin_preferences")
        .select("*")
        .eq("admin_id", user.id)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      set({ adminPreferences: data || null });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  updateAdminProfile: async (profile: Partial<AdminProfile>) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("No user found");

      const { error } = await supabase.from("admin_profiles").upsert({
        id: user.id,
        ...profile,
      });

      if (error) throw error;
      await get().fetchAdminProfile();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  updateAdminPreferences: async (preferences: Partial<AdminPreferences>) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("No user found");

      const { error } = await supabase.from("admin_preferences").upsert({
        admin_id: user.id,
        ...preferences,
      });

      if (error) throw error;
      await get().fetchAdminPreferences();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  updateCandidateStatus: async (
    candidateId: string,
    status: CandidateStatus
  ) => {
    try {
      const { error } = await supabase
        .from("candidates")
        .update({ status })
        .eq("id", candidateId);

      if (error) throw error;

      // Add status change to application logs
      await get().addApplicationLog(
        candidateId,
        status,
        `Status updated to ${status}`
      );
      await get().fetchCandidates();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  updateCandidateRating: async (candidateId: string, rating: number) => {
    try {
      const { error } = await supabase
        .from("candidates")
        .update({ rating })
        .eq("id", candidateId);

      if (error) throw error;
      await get().fetchCandidates();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  addApplicationLog: async (
    candidateId: string,
    status: CandidateStatus,
    notes: string
  ) => {
    try {
      const { error } = await supabase.from("application_logs").insert({
        candidate_id: candidateId,
        status,
        notes,
        created_at: new Date().toISOString(),
      });

      if (error) throw error;
      await get().fetchApplicationLogs(candidateId);
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  setFilters: (filters) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
    }));
  },
}));
